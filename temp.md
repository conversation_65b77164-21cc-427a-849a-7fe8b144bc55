Starting control programs... v0.0.11
Program Manager disabled - running Cloud Connector only
QoS level: 1
PDS time: 120 seconds
Loaded MQTT configurations:
  Server Config:
    Broker: rado-dev-broker.1iot.io:8885
    Username: platform_user
    Client ID: go-mqtt-server
    Subscribe topics: 3
    Publish topics: 3

  Client Config:
    Broker: paradox-dev-broker.1iot.io:8885
    Username: platform_user
    Client ID: go-mqtt-client
    Subscribe topics: 3
    Publish topics: 2

✓ IO Module initialized
✓ Aggregator Client initialized
✓ Message Bridge: Connected IO and Aggregator modules
Connecting to MQTT broker (IO-program) - attempt 1/5...
Successfully connected to MQTT broker (IO-program)
✓ IO Module started
Connecting to MQTT broker (Aggregator-client) - attempt 1/5...
IO: Connected to firmware MQTT broker

Subscribing to 3 IO-program topics...
  - readData/periodic/res
  - readData/res
  - writeData/res
Successfully connected to MQTT broker (Aggregator-client)
✓ Aggregator Client started
✓ Message Bridge: Started
Aggregator: Started processing channel messages
Aggregator: Connected to cloud MQTT broker

Subscribing to 3 Aggregator-client topics...
  - argus/bangalore/aatmunnOffice/pullSensorData
  - argus/bangalore/aatmunnOffice/reqControlData
  - argus/bangalore/aatmunnOffice/getConfig
Aggregator: Sent to cloud topic argus/bangalore/aatmunnOffice/config
✓ Argus Cloud Connector is running