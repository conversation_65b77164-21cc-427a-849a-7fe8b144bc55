package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"program-manager/ceb"
	"program-manager/config"
	"program-manager/redis"
	"program-manager/types"
)

// run_ceb_tests.go - Comprehensive test runner for CEB system
func main() {
	fmt.Println("🧪 CEB System Test Runner")
	fmt.Println(strings.Repeat("=", 50))

	// Initialize Redis
	redis.Initialize()
	ctx := context.Background()

	// Run test suite
	runTestSuite(ctx)
}

func runTestSuite(ctx context.Context) {
	tests := []struct {
		name string
		fn   func(context.Context) error
	}{
		{"Basic CEB Functionality", testBasicCEBFunctionality},
		{"All 9 Outputs Generation", testAll9Outputs},
		{"Heating Scenario", testHeatingScenario},
		{"Cooling Scenario", testCoolingScenario},
		{"Dehumidification Scenario", testDehumidificationScenario},
		{"Feed-Forward Effects", testFeedForwardEffects},
		{"Emergency Stop", testEmergencyStop},
		{"PID Controller Behavior", testPIDControllerBehavior},
		{"Sensor Integration", testSensorIntegration},
		{"Configuration Loading", testConfigurationLoading},
	}

	passed := 0
	failed := 0

	for _, test := range tests {
		fmt.Printf("\n🔍 Running: %s\n", test.name)

		err := test.fn(ctx)
		if err != nil {
			fmt.Printf("❌ FAILED: %s - %v\n", test.name, err)
			failed++
		} else {
			fmt.Printf("✅ PASSED: %s\n", test.name)
			passed++
		}
	}

	fmt.Printf("\n%s", strings.Repeat("=", 50))
	fmt.Printf("\n📊 Test Results: %d passed, %d failed\n", passed, failed)

	if failed > 0 {
		os.Exit(1)
	}
}

func testBasicCEBFunctionality(ctx context.Context) error {
	// Create CEB controller
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	if !controller.IsEnabled() {
		return fmt.Errorf("controller should be enabled")
	}

	// Set up basic test data
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue := func(name string, value float64) {
		if setpoint, found := topics.GetSetpointByName(name); found {
			redis.SetFloat(ctx, setpoint.RedisKey, value)
		}
	}
	setSetpointValue("heating_target", 22.0)
	setSetpointValue("cooling_target", 26.0)
	setSetpointValue("dehumidify_target", 70.0)
	redis.SetFloat(ctx, topics.SensorTemperature, 21.0)
	redis.SetFloat(ctx, topics.SensorHumidity, 65.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 15.0)
	redis.SetFloat(ctx, topics.WeatherLightLevel, 300.0)

	// Run processing cycle
	err := controller.ProcessCycle(ctx)
	if err != nil {
		return fmt.Errorf("processing cycle failed: %v", err)
	}

	fmt.Printf("   ✓ CEB controller created and cycle executed successfully\n")
	return nil
}

func testAll9Outputs(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Set up test data
	setupTestData(ctx)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return fmt.Errorf("processing cycle failed: %v", err)
	}

	// Get Redis topics configuration
	topics := config.CreateDefaultRedisTopicsConfig()

	// Verify all 9 outputs exist
	outputs := []struct {
		key  string
		name string
	}{
		{topics.VentTempControl, "1. Ventilation Required for Temperature Control"},
		{topics.VentHumidityControl, "2. Ventilation Required for Humidity Control"},
		{topics.HighestVentRequest, "3. Highest Ventilation Request"},
		{topics.SumVentRequests, "4. Sum of Ventilation Requests"},
		{topics.HeatTempControl, "5. Heating Required for Temperature Control"},
		{topics.HeatHumidityControl, "6. Heating Required for Humidity Control"},
		{topics.HighestHeatRequest, "7. Highest Heating Request"},
		{topics.SumHeatRequests, "8. Sum of Heating Requests"},
		{topics.HeatingSystemTempRequest, "9. Current Temperature Request to Heating System"},
	}

	fmt.Printf("   📋 Verifying all 9 Energy Balance outputs:\n")
	for _, output := range outputs {
		value, err := redis.GetFloat(ctx, output.key)
		if err != nil {
			return fmt.Errorf("output %s not found: %v", output.name, err)
		}
		fmt.Printf("      %s: %.1f\n", output.name, value)
	}

	return nil
}

func testHeatingScenario(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Cold scenario requiring heating
	topics := config.CreateDefaultRedisTopicsConfig()
	if setpoint, found := topics.GetSetpointByPurpose(types.PurposeHeatingTarget); found {
		redis.SetFloat(ctx, setpoint.RedisKey, 22.0)
	}
	redis.SetFloat(ctx, topics.SensorTemperature, 19.0) // Below target
	redis.SetFloat(ctx, topics.SensorHumidity, 60.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 5.0)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return err
	}

	heatDemand, _ := redis.GetFloat(ctx, topics.HeatTempControl)
	heatSysTemp, _ := redis.GetFloat(ctx, topics.HeatingSystemTempRequest)

	if heatDemand <= 0 {
		return fmt.Errorf("expected heating demand in cold scenario, got %.1f", heatDemand)
	}

	if heatSysTemp < 30.0 || heatSysTemp > 80.0 {
		return fmt.Errorf("heating system temperature out of range: %.1f°C (should be 30-80°C)", heatSysTemp)
	}

	fmt.Printf("   🔥 Heating demand: %.1f%%, System temp: %.1f°C\n", heatDemand, heatSysTemp)
	return nil
}

func testCoolingScenario(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Hot scenario requiring cooling
	topics := config.CreateDefaultRedisTopicsConfig()
	if setpoint, found := topics.GetSetpointByPurpose(types.PurposeCoolingTarget); found {
		redis.SetFloat(ctx, setpoint.RedisKey, 26.0)
	}
	redis.SetFloat(ctx, topics.SensorTemperature, 28.0) // Above target
	redis.SetFloat(ctx, topics.SensorHumidity, 60.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 30.0)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return err
	}

	ventDemand, _ := redis.GetFloat(ctx, topics.VentTempControl)
	heatDemand, _ := redis.GetFloat(ctx, topics.HeatTempControl)

	if ventDemand <= 0 {
		return fmt.Errorf("expected ventilation demand in hot scenario, got %.1f", ventDemand)
	}

	// Note: The system may still have heating demand due to feed-forward effects
	// This is correct behavior - outdoor temperature effects are applied regardless of current temperature
	// The PDF specifies that feed-forward effects are always active
	if heatDemand > 50.0 {
		return fmt.Errorf("heating demand seems excessive in hot scenario: %.1f", heatDemand)
	}

	fmt.Printf("   🌬️  Ventilation demand: %.1f%%, Heating: %.1f%%\n", ventDemand, heatDemand)
	return nil
}

func testDehumidificationScenario(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// High humidity scenario
	topics := config.CreateDefaultRedisTopicsConfig()
	if setpoint, found := topics.GetSetpointByPurpose(types.PurposeDehumidifyHeatTarget); found {
		redis.SetFloat(ctx, setpoint.RedisKey, 70.0)
	}
	redis.SetFloat(ctx, topics.SensorTemperature, 23.0) // In deadband
	redis.SetFloat(ctx, topics.SensorHumidity, 80.0)    // Above target
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 15.0)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return err
	}

	heatHumid, _ := redis.GetFloat(ctx, topics.HeatHumidityControl)
	ventHumid, _ := redis.GetFloat(ctx, topics.VentHumidityControl)

	if heatHumid <= 0 {
		return fmt.Errorf("expected heating for humidity control, got %.1f", heatHumid)
	}

	if ventHumid <= 0 {
		return fmt.Errorf("expected ventilation for humidity control, got %.1f", ventHumid)
	}

	fmt.Printf("   💧 Dehumidification - Heat: %.1f%%, Vent: %.1f%%\n", heatHumid, ventHumid)
	return nil
}

func testFeedForwardEffects(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	controller := ceb.NewCEBController(cebConfig)

	// Test with extreme outdoor conditions
	topics := config.CreateDefaultRedisTopicsConfig()
	redis.SetFloat(ctx, topics.SensorTemperature, 21.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, -10.0) // Very cold
	redis.SetFloat(ctx, topics.WeatherLightLevel, 900.0)  // Very bright

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return err
	}

	topics := config.CreateDefaultRedisTopicsConfig()
	heatDemand, _ := redis.GetFloat(ctx, topics.HeatTempControl)
	ventDemand, _ := redis.GetFloat(ctx, topics.VentTempControl)

	fmt.Printf("   🌡️  Feed-forward effects - Heat: %.1f%%, Vent: %.1f%%\n", heatDemand, ventDemand)
	fmt.Printf("      (Cold outdoor + high light should affect demands)\n")
	return nil
}

func testEmergencyStop(ctx context.Context) error {
	cebConfig := config.CreateEnhancedCEBConfig()
	cebConfig.SafetyLimits.EmergencyStop = true
	controller := ceb.NewCEBController(cebConfig)

	// Set up scenario that would create demands
	setupTestData(ctx)

	err := controller.ProcessCycle(ctx)
	if err != nil {
		return err
	}

	// All outputs should be zero
	topics := config.CreateDefaultRedisTopicsConfig()
	heatDemand, _ := redis.GetFloat(ctx, topics.HighestHeatRequest)
	ventDemand, _ := redis.GetFloat(ctx, topics.HighestVentRequest)

	if heatDemand != 0 || ventDemand != 0 {
		return fmt.Errorf("emergency stop should zero all outputs, got heat=%.1f, vent=%.1f", heatDemand, ventDemand)
	}

	fmt.Printf("   🛑 Emergency stop correctly zeroed all outputs\n")
	return nil
}

func testPIDControllerBehavior(ctx context.Context) error {
	// Test PID controller directly
	pidConfig := config.CreateEnhancedCEBConfig().TemperaturePID
	pid := ceb.NewPIDController(pidConfig)

	// Test basic PID response
	error1 := 2.0
	output1 := pid.Calculate(error1) // First call initializes

	time.Sleep(10 * time.Millisecond)
	output2 := pid.Calculate(error1) // Second call should give response

	if output2 <= 0 {
		return fmt.Errorf("expected positive PID output for positive error")
	}

	// Test error reduction
	error2 := 1.0
	time.Sleep(10 * time.Millisecond)
	output3 := pid.Calculate(error2)

	if output3 >= output2 {
		return fmt.Errorf("PID output should decrease as error decreases")
	}

	fmt.Printf("   🎛️  PID response: %.1f → %.1f → %.1f (error: 2.0 → 2.0 → 1.0)\n", output1, output2, output3)
	return nil
}

func testSensorIntegration(ctx context.Context) error {
	sensorConfig := config.CreateEnhancedCEBConfig().SensorIntegration
	buffer := ceb.NewSensorBuffer(sensorConfig)

	// Test temperature integration
	temps := []float64{20.0, 21.0, 22.0, 21.5, 20.5}
	var integrated float64

	for _, temp := range temps {
		integrated = buffer.IntegrateTemperature(temp)
	}

	if integrated < 20.5 || integrated > 21.5 {
		return fmt.Errorf("sensor integration out of expected range: %.1f", integrated)
	}

	fmt.Printf("   📊 Sensor integration: %.1f°C (from %v)\n", integrated, temps)
	return nil
}

func testConfigurationLoading(ctx context.Context) error {
	// Test loading enhanced configuration
	cebConfig, err := config.LoadCEBConfig("config/cebConfig.json")
	if err != nil {
		return fmt.Errorf("failed to load enhanced CEB config: %v", err)
	}

	if cebConfig.ProgramName != "climate energy balance" {
		return fmt.Errorf("expected program name 'climate energy balance', got '%s'", cebConfig.ProgramName)
	}

	if !cebConfig.Enabled {
		return fmt.Errorf("CEB should be enabled in config")
	}

	fmt.Printf("   ⚙️  Enhanced Configuration loaded: %s (enabled: %v)\n", cebConfig.ProgramName, cebConfig.Enabled)
	return nil
}

func setupTestData(ctx context.Context) {
	topics := config.CreateDefaultRedisTopicsConfig()
	setSetpointValue := func(purpose types.SetpointPurpose, value float64) {
		if setpoint, found := topics.GetSetpointByPurpose(purpose); found {
			redis.SetFloat(ctx, setpoint.RedisKey, value)
		}
	}
	setSetpointValue(types.PurposeHeatingTarget, 22.0)
	setSetpointValue(types.PurposeCoolingTarget, 26.0)
	setSetpointValue(types.PurposeDehumidifyTarget, 70.0)
	setSetpointValue(types.PurposeHumidifyTarget, 35.0)
	setSetpointValue(types.PurposeMaxDehumidVent, 20.0)
	setSetpointValue(types.PurposeMaxDehumidHeat, 10.0)
	setSetpointValue(types.PurposeCO2Target, 800.0)
	redis.SetFloat(ctx, topics.SensorTemperature, 21.0)
	redis.SetFloat(ctx, topics.SensorHumidity, 65.0)
	redis.SetFloat(ctx, topics.WeatherOutdoorTemp, 15.0)
	redis.SetFloat(ctx, topics.WeatherLightLevel, 300.0)
}
