# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
bin/
tmp/
build/
dist/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Log files
*.log
logs/
weather_sim.log
monitor.log

# Process ID files
*.pid
weather_sim.pid
monitor.pid

# Environment files
.env
.env.local
.env.*.local

# Redis dump files
dump.rdb
appendonly.aof

# Temporary files
*.tmp
*.temp
temp/
temp.*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Coverage reports
coverage.out
coverage.html
*.cover

# Profiling data
*.prof
*.pprof

# Air live reload tool
tmp/

# Local configuration overrides
config/local_*.json
config/*_local.json

# Backup files
*.bak
*.backup
*~

# Node.js (if any frontend tools are used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if any Python scripts are used)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Documentation build artifacts
docs/_build/
docs/site/

# Test artifacts
test_results/
test_output/

# Local development files
.local/
local/

# Certificate files (if any)
*.pem
*.key
*.crt
*.p12

# Database files
*.db
*.sqlite
*.sqlite3

# Archive files
*.zip
*.tar.gz
*.rar

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
Session.vim

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local Redis data
redis-data/
redis.conf.local

# Performance monitoring
*.trace

# Go module proxy cache
GOPROXY=
GOSUMDB=

# Air (live reload) temporary directory
tmp/

# Delve debugger
__debug_bin

# GoLand
.idea/

# Local testing artifacts
test_*.json
*_test_output.txt

# Benchmark results
*.bench

# Memory profiles
*.mprof

# CPU profiles
*.cpuprof

# Trace files
*.trace

# Local scripts
run_local.sh
test_local.sh
deploy_local.sh

# Docker artifacts (if Docker is used)
.dockerignore
docker-compose.override.yml

# Kubernetes artifacts (if K8s is used)
*.kubeconfig
kube-config

# Terraform (if infrastructure as code is used)
*.tfstate
*.tfstate.*
.terraform/

# Local development certificates
localhost.pem
localhost-key.pem

# macOS
.AppleDouble
.LSOverride

# Windows
desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Project-specific ignores
# Add any project-specific files or directories that should be ignored

# Local configuration files that might contain sensitive data
config/secrets.json
config/production.json
config/staging.json

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Local Netlify folder
.netlify
