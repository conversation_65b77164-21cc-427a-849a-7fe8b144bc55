package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"program-manager/types"
)

// ConfigManager provides generic configuration loading and saving functionality
type ConfigManager[T any] struct {
	filename       string
	defaultFactory func() T
	validator      func(T) error
}

// NewConfigManager creates a new configuration manager for type T
func NewConfigManager[T any](filename string, defaultFactory func() T, validator func(T) error) *ConfigManager[T] {
	return &ConfigManager[T]{
		filename:       filename,
		defaultFactory: defaultFactory,
		validator:      validator,
	}
}

// Load loads configuration from file
func (cm *ConfigManager[T]) Load() (T, error) {
	var config T

	file, err := os.Open(cm.filename)
	if err != nil {
		return config, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return config, err
	}

	// Validate loaded configuration
	if cm.validator != nil {
		if err := cm.validator(config); err != nil {
			return config, fmt.Errorf("configuration validation failed: %v", err)
		}
	}

	return config, nil
}

// Save saves configuration to file
func (cm *ConfigManager[T]) Save(config T) error {
	// Validate before saving
	if cm.validator != nil {
		if err := cm.validator(config); err != nil {
			return fmt.Errorf("configuration validation failed: %v", err)
		}
	}

	file, err := os.Create(cm.filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(config)
}

// ValidateTimeFormat validates time format
func ValidateTimeFormat(timeStr string) error {
	_, err := time.Parse("15:04", timeStr)
	return err
}

// ValidateInstancePeriodOverlaps validates period overlaps within an instance
func ValidateInstancePeriodOverlaps(instance types.DiurnalInstance) error {
	if !instance.Enabled || len(instance.Periods) == 0 {
		return nil
	}

	periods := instance.Periods
	for i := 0; i < len(periods)-1; i++ {
		for j := i + 1; j < len(periods); j++ {
			// Check if periods have any common days
			hasCommonDay := false
			for day := range periods[i].ActiveDays {
				if periods[i].ActiveDays[day] && periods[j].ActiveDays[day] {
					hasCommonDay = true
					break
				}
			}

			if hasCommonDay {
				// Skip validation for relative times - they will be resolved at runtime
				if IsRelativeTime(periods[i].StartTime) || IsRelativeTime(periods[i].EndTime) ||
					IsRelativeTime(periods[j].StartTime) || IsRelativeTime(periods[j].EndTime) {
					continue
				}

				startA, _ := time.Parse("15:04", periods[i].StartTime)
				endA, _ := time.Parse("15:04", periods[i].EndTime)
				startB, _ := time.Parse("15:04", periods[j].StartTime)
				endB, _ := time.Parse("15:04", periods[j].EndTime)

				// Check if periods overlap or are adjacent
				if (startA.Before(endB) || startA.Equal(endB)) &&
					(endA.After(startB) || endA.Equal(startB)) {
					return errors.New("instance " + instance.InstanceId +
						": periods " + periods[i].PeriodId + " and " +
						periods[j].PeriodId + " overlap or are adjacent")
				}
			}
		}
	}
	return nil
}

// IsRelativeTime checks if a time string contains relative references
func IsRelativeTime(timeStr string) bool {
	return strings.Contains(timeStr, "beforeDawn") ||
		strings.Contains(timeStr, "afterDawn") ||
		strings.Contains(timeStr, "beforeDusk") ||
		strings.Contains(timeStr, "afterDusk")
}

// ValidateLoadedConfig validates loaded enhanced configuration
func ValidateLoadedConfig(config types.DiurnalSetpointConfig) error {
	// Check number of instances
	if len(config.Instances) == 0 {
		return errors.New("no instances defined")
	}
	if len(config.Instances) > 8 {
		return errors.New("too many instances (maximum 8 allowed)")
	}

	// Track used IDs to check for duplicates
	usedIDs := make(map[string]bool)

	// Validate each instance
	for _, instance := range config.Instances {
		// Validate instance ID for duplicates
		if usedIDs[instance.InstanceId] {
			return errors.New("duplicate instance ID found: " + instance.InstanceId)
		}
		usedIDs[instance.InstanceId] = true

		// Validate instance ID is not empty
		if instance.InstanceId == "" {
			return errors.New("instance ID cannot be empty")
		}

		// Validate program name
		if instance.ProgramName == "" {
			return errors.New("instance " + instance.InstanceId + " has no program name")
		}

		// If instance is enabled, validate its configuration
		if instance.Enabled {
			if len(instance.Periods) == 0 {
				return errors.New("instance " + instance.InstanceId +
					" is enabled but has no periods")
			}
			if len(instance.Periods) > 8 {
				return errors.New("instance " + instance.InstanceId +
					" has too many periods (maximum 8 allowed)")
			}

			// Track used period IDs within this instance
			usedPeriodIDs := make(map[string]bool)

			// Validate each period
			for _, period := range instance.Periods {
				// Validate period ID for duplicates within instance
				if usedPeriodIDs[period.PeriodId] {
					return errors.New("instance " + instance.InstanceId +
						": duplicate period ID found: " + period.PeriodId)
				}
				usedPeriodIDs[period.PeriodId] = true

				// Validate period ID is not empty
				if period.PeriodId == "" {
					return errors.New("instance " + instance.InstanceId +
						": period ID cannot be empty")
				}

				// Validate period name
				if period.Name == "" {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId + " has no name")
				}

				// Validate period status
				validStatuses := map[string]bool{
					"active": true, "inactive": true, "disabled": true,
					"externalNotActive": true, "notUsedOverlap": true,
				}
				if !validStatuses[period.PeriodStatus] {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId +
						" has invalid status: " + period.PeriodStatus)
				}

				// Validate time format (enhanced to support relative times)
				if !IsRelativeTime(period.StartTime) {
					if err := ValidateTimeFormat(period.StartTime); err != nil {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid start time: " + err.Error())
					}
				}
				if !IsRelativeTime(period.EndTime) {
					if err := ValidateTimeFormat(period.EndTime); err != nil {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid end time: " + err.Error())
					}
				}

				// Validate active days
				validDays := map[string]bool{
					"monday": true, "tuesday": true, "wednesday": true,
					"thursday": true, "friday": true, "saturday": true, "sunday": true,
				}
				for day := range period.ActiveDays {
					if !validDays[day] {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid day: " + day)
					}
				}

				// Validate setpoints
				if len(period.Setpoints) == 0 {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId + " has no setpoints")
				}
				if len(period.Setpoints) > 8 {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId +
						" has too many setpoints (maximum 8 allowed)")
				}
			}

			// Validate period overlaps within this instance
			if err := ValidateInstancePeriodOverlaps(instance); err != nil {
				return err
			}
		}
	}

	return nil
}

// Simplified configuration loading and saving functions using the generic ConfigManager

// LoadDiurnalConfig loads diurnal configuration from file
func LoadDiurnalConfig(filename string) (types.DiurnalSetpointConfig, error) {
	cm := NewConfigManager[types.DiurnalSetpointConfig](filename, nil, ValidateLoadedConfig)
	return cm.Load()
}

// SaveDiurnalConfig saves diurnal configuration to file
func SaveDiurnalConfig(filename string, config types.DiurnalSetpointConfig) error {
	cm := NewConfigManager[types.DiurnalSetpointConfig](filename, nil, ValidateLoadedConfig)
	return cm.Save(config)
}

// LoadClockConfig loads clock configuration from file
func LoadClockConfig(filename string) (types.ClockConfig, error) {
	cm := NewConfigManager[types.ClockConfig](filename, nil, nil)
	return cm.Load()
}

// SaveClockConfig saves clock configuration to file
func SaveClockConfig(filename string, config types.ClockConfig) error {
	cm := NewConfigManager[types.ClockConfig](filename, nil, nil)
	return cm.Save(config)
}

// LoadCEBConfig loads CEB configuration from file
func LoadCEBConfig(filename string) (types.CEBConfig, error) {
	cm := NewConfigManager[types.CEBConfig](filename, nil, nil)
	return cm.Load()
}

// SaveCEBConfig saves CEB configuration to file
func SaveCEBConfig(filename string, config types.CEBConfig) error {
	cm := NewConfigManager[types.CEBConfig](filename, nil, nil)
	return cm.Save(config)
}

// LoadProgramManager loads program manager configuration from file
func LoadProgramManager(filename string) (map[string]interface{}, error) {
	cm := NewConfigManager[map[string]interface{}](filename, nil, nil)
	return cm.Load()
}

// SaveProgramManager saves program manager configuration to file
func SaveProgramManager(filename string, programManager map[string]interface{}) error {
	cm := NewConfigManager[map[string]interface{}](filename, nil, nil)
	return cm.Save(programManager)
}
